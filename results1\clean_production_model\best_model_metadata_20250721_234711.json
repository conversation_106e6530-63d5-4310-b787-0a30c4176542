{"algorithm_name": "Random Forest", "accuracy": 0.9322033898305084, "f1_score": 0.9349450128536598, "cv_accuracy_mean": 0.956799259944496, "cv_accuracy_std": 0.027283431165365794, "cv_f1_mean": 0.9575143989479654, "cv_f1_std": 0.026889943492639724, "feature_count": 18, "timestamp": "2025-07-21T23:47:11.257402", "model_type": "RandomForestClassifier", "is_best_model": true, "algorithm_key": "random_forest", "random_state": 42, "dataset_path": "dataset/processed/safe_ml_fatigue_dataset.csv", "target_column": "fatigue_risk", "train_samples": 232, "test_samples": 59, "shap_top_features": ["productivity_points", "strava_title_count", "gamification_balance", "achievement_rate", "total_distance_km"]}