"""
Comprehensive Visualization Suite for SHAP-based Analysis (main5.py)
Updated to work with the new SHAP ablation study results from main5.py

This module provides comprehensive visualizations for:
1. SHAP feature importance analysis
2. Model performance comparison across algorithms
3. Feature selection validation
4. Cross-validation and overfitting analysis
5. Bias-corrected fatigue prediction results

Compatible with main5.py SHAP-based pipeline results.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import logging
import warnings
warnings.filterwarnings('ignore')

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import visualization modules
import comp_feat_test as cft
import imp_feat_test as ift
import all_plot as ap
import kfold_overfitting as kfold

class SHAPVisualizationSuite:
    """Comprehensive visualization suite for SHAP-based analysis results"""

    def __init__(self):
        """Initialize the visualization suite"""
        self.shap_results_dir = Path("results/shap_ablation_study")
        self.output_dir = Path("results/shap_visualizations")
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # Data paths for different datasets
        self.data_paths = {
            'bias_corrected': 'dataset/processed/safe_ml_bias_corrected_dataset.csv',
            'regular_fatigue': 'dataset/processed/safe_ml_fatigue_dataset.csv',
            'title_only': 'dataset/processed/safe_ml_title_only_dataset.csv'
        }

        # Target columns for different datasets
        self.target_columns = {
            'bias_corrected': 'corrected_fatigue_risk',
            'regular_fatigue': 'fatigue_risk',
            'title_only': 'title_fatigue_risk'
        }

        self.results = {}

    def detect_available_data(self):
        """Detect which datasets are available"""
        available_data = {}

        for data_type, data_path in self.data_paths.items():
            if Path(data_path).exists():
                available_data[data_type] = {
                    'path': data_path,
                    'target': self.target_columns[data_type]
                }
                logger.info(f"✅ Found {data_type} dataset: {data_path}")
            else:
                logger.warning(f"❌ Missing {data_type} dataset: {data_path}")

        return available_data

    def load_shap_results(self):
        """Load SHAP ablation study results"""
        if not self.shap_results_dir.exists():
            logger.error(f"❌ SHAP results directory not found: {self.shap_results_dir}")
            return None

        # Find the most recent SHAP results
        result_files = list(self.shap_results_dir.glob("shap_results_*.csv"))
        report_files = list(self.shap_results_dir.glob("shap_report_*.txt"))

        if not result_files:
            logger.error("❌ No SHAP results files found")
            return None

        # Use the most recent file
        latest_result = max(result_files, key=lambda x: x.stat().st_mtime)
        latest_report = max(report_files, key=lambda x: x.stat().st_mtime) if report_files else None

        logger.info(f"📊 Loading SHAP results from: {latest_result}")

        try:
            # Load results CSV
            shap_data = pd.read_csv(latest_result)

            # Load report if available
            report_content = None
            if latest_report:
                with open(latest_report, 'r', encoding='utf-8') as f:
                    report_content = f.read()
                logger.info(f"📋 Loaded SHAP report from: {latest_report}")

            return {
                'results_data': shap_data,
                'report_content': report_content,
                'results_file': latest_result,
                'report_file': latest_report
            }

        except Exception as e:
            logger.error(f"❌ Error loading SHAP results: {e}")
            return None

    def create_shap_summary_visualization(self, shap_results):
        """Create comprehensive SHAP summary visualization"""
        logger.info("🎨 Creating SHAP summary visualization...")

        if not shap_results or 'results_data' not in shap_results:
            logger.error("❌ No SHAP results data available")
            return

        # Create figure with subplots
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('SHAP Ablation Study - Comprehensive Summary', fontsize=16, fontweight='bold')

        # Extract data from report if available
        if shap_results['report_content']:
            self._plot_algorithm_performance(axes[0, 0], shap_results['report_content'])
            self._plot_feature_importance_comparison(axes[0, 1], shap_results['report_content'])
            self._plot_feature_consistency(axes[1, 0], shap_results['report_content'])
            self._plot_recommendations(axes[1, 1], shap_results['report_content'])
        else:
            # Fallback to basic visualization
            for ax in axes.flat:
                ax.text(0.5, 0.5, 'SHAP Report Not Available\nRun main5.py to generate results',
                       ha='center', va='center', transform=ax.transAxes, fontsize=12)
                ax.set_xticks([])
                ax.set_yticks([])

        plt.tight_layout()

        # Save plot
        output_file = self.output_dir / 'shap_summary_visualization.png'
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        logger.info(f"✅ Saved SHAP summary visualization: {output_file}")

        plt.show()

    def _plot_algorithm_performance(self, ax, report_content):
        """Plot algorithm performance from SHAP report"""
        try:
            # Extract performance data from report
            algorithms = []
            accuracies = []
            f1_scores = []

            lines = report_content.split('\n')
            in_performance_section = False

            for line in lines:
                if '🤖 MODEL PERFORMANCE:' in line:
                    in_performance_section = True
                    continue
                elif '🎯 GLOBAL SHAP FEATURE IMPORTANCE:' in line:
                    in_performance_section = False
                    break

                if in_performance_section and 'Test Accuracy:' in line:
                    # Extract algorithm name from previous lines
                    for prev_line in reversed(lines[:lines.index(line)]):
                        if '•' in prev_line and ':' in prev_line:
                            algo_name = prev_line.split('•')[1].split(':')[0].strip()
                            algorithms.append(algo_name)
                            break

                    # Extract accuracy
                    accuracy = float(line.split('Test Accuracy:')[1].strip())
                    accuracies.append(accuracy)

                elif in_performance_section and 'Test F1-Score:' in line:
                    # Extract F1 score
                    f1_score = float(line.split('Test F1-Score:')[1].strip())
                    f1_scores.append(f1_score)

            if algorithms and accuracies:
                x = np.arange(len(algorithms))
                width = 0.35

                bars1 = ax.bar(x - width/2, accuracies, width, label='Accuracy', alpha=0.8)
                bars2 = ax.bar(x + width/2, f1_scores, width, label='F1-Score', alpha=0.8)

                ax.set_xlabel('Algorithms')
                ax.set_ylabel('Score')
                ax.set_title('Algorithm Performance Comparison')
                ax.set_xticks(x)
                ax.set_xticklabels(algorithms, rotation=45, ha='right')
                ax.legend()
                ax.grid(True, alpha=0.3)

                # Add value labels
                for bars in [bars1, bars2]:
                    for bar in bars:
                        height = bar.get_height()
                        ax.text(bar.get_x() + bar.get_width()/2, height + 0.01,
                               f'{height:.3f}', ha='center', va='bottom', fontsize=8)
            else:
                ax.text(0.5, 0.5, 'Performance data not found', ha='center', va='center', transform=ax.transAxes)

        except Exception as e:
            logger.error(f"Error plotting algorithm performance: {e}")
            ax.text(0.5, 0.5, f'Error: {str(e)}', ha='center', va='center', transform=ax.transAxes)

    def _plot_feature_importance_comparison(self, ax, report_content):
        """Plot top features comparison across algorithms"""
        try:
            # Extract top 5 features for each algorithm
            lines = report_content.split('\n')
            current_algo = None
            algo_features = {}

            for line in lines:
                if '📊' in line and ':' in line:
                    current_algo = line.split('📊')[1].split(':')[0].strip()
                    algo_features[current_algo] = []
                elif current_algo and '⭐' in line and ':' in line:
                    # Extract feature name and importance
                    parts = line.split('⭐')[1].split(':')
                    if len(parts) >= 2:
                        feature_name = parts[0].strip()
                        importance = float(parts[1].strip())
                        algo_features[current_algo].append((feature_name, importance))

                        # Only take top 5
                        if len(algo_features[current_algo]) >= 5:
                            current_algo = None

            if algo_features:
                # Get all unique features
                all_features = set()
                for features in algo_features.values():
                    all_features.update([f[0] for f in features])

                # Create heatmap data
                feature_list = list(all_features)[:10]  # Top 10 most common
                algo_list = list(algo_features.keys())

                heatmap_data = np.zeros((len(feature_list), len(algo_list)))

                for j, algo in enumerate(algo_list):
                    for i, feature in enumerate(feature_list):
                        # Find importance for this feature in this algorithm
                        for feat_name, importance in algo_features[algo]:
                            if feat_name == feature:
                                heatmap_data[i, j] = importance
                                break

                # Create heatmap
                sns.heatmap(heatmap_data,
                           xticklabels=algo_list,
                           yticklabels=[f[:20] + '...' if len(f) > 20 else f for f in feature_list],
                           annot=True, fmt='.3f', cmap='YlOrRd', ax=ax)
                ax.set_title('Feature Importance Heatmap')
                ax.set_xlabel('Algorithms')
                ax.set_ylabel('Features')
            else:
                ax.text(0.5, 0.5, 'Feature importance data not found', ha='center', va='center', transform=ax.transAxes)

        except Exception as e:
            logger.error(f"Error plotting feature importance: {e}")
            ax.text(0.5, 0.5, f'Error: {str(e)}', ha='center', va='center', transform=ax.transAxes)

    def _plot_feature_consistency(self, ax, report_content):
        """Plot feature consistency across algorithms"""
        try:
            # Extract consistency data
            lines = report_content.split('\n')
            in_consistency_section = False
            features = []
            percentages = []

            for line in lines:
                if '🎖️ FEATURE CONSISTENCY ACROSS ALGORITHMS:' in line:
                    in_consistency_section = True
                    continue
                elif '✅ SHAP-BASED RECOMMENDATIONS:' in line:
                    in_consistency_section = False
                    break

                if in_consistency_section and '•' in line and '%' in line:
                    parts = line.split('•')[1].split(':')
                    if len(parts) >= 2:
                        feature_name = parts[0].strip()
                        percentage_text = parts[1].strip()
                        if '(' in percentage_text and '%' in percentage_text:
                            percentage = float(percentage_text.split('(')[1].split('%')[0])
                            features.append(feature_name)
                            percentages.append(percentage)

            if features and percentages:
                # Create bar plot
                bars = ax.bar(range(len(features)), percentages, alpha=0.7, color='skyblue')
                ax.set_xlabel('Features')
                ax.set_ylabel('Consistency (%)')
                ax.set_title('Feature Consistency Across Algorithms')
                ax.set_xticks(range(len(features)))
                ax.set_xticklabels([f[:15] + '...' if len(f) > 15 else f for f in features],
                                  rotation=45, ha='right')
                ax.grid(True, alpha=0.3)

                # Add value labels
                for bar, pct in zip(bars, percentages):
                    ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                           f'{pct:.0f}%', ha='center', va='bottom')
            else:
                ax.text(0.5, 0.5, 'Consistency data not found', ha='center', va='center', transform=ax.transAxes)

        except Exception as e:
            logger.error(f"Error plotting feature consistency: {e}")
            ax.text(0.5, 0.5, f'Error: {str(e)}', ha='center', va='center', transform=ax.transAxes)

    def _plot_recommendations(self, ax, report_content):
        """Plot SHAP recommendations"""
        try:
            # Extract recommendations
            lines = report_content.split('\n')
            in_recommendations_section = False
            best_algo = None
            best_accuracy = None
            top_features = []

            for line in lines:
                if '✅ SHAP-BASED RECOMMENDATIONS:' in line:
                    in_recommendations_section = True
                    continue
                elif '📊 SHAP ANALYSIS SUMMARY:' in line:
                    in_recommendations_section = False
                    break

                if in_recommendations_section:
                    if 'Best performing algorithm:' in line:
                        parts = line.split('Best performing algorithm:')[1].strip()
                        if '(' in parts:
                            best_algo = parts.split('(')[0].strip()
                            best_accuracy = float(parts.split('(')[1].split(' ')[0])
                    elif line.strip().startswith(('1.', '2.', '3.', '4.', '5.')):
                        # Extract feature name
                        feature_name = line.split('.')[1].split('(')[0].strip()
                        top_features.append(feature_name)

            # Create text-based visualization
            ax.axis('off')

            if best_algo and best_accuracy:
                text_content = f"🏆 SHAP RECOMMENDATIONS\n\n"
                text_content += f"Best Algorithm: {best_algo}\n"
                text_content += f"Best Accuracy: {best_accuracy:.4f}\n\n"

                if top_features:
                    text_content += "Top 5 Features:\n"
                    for i, feature in enumerate(top_features[:5], 1):
                        text_content += f"{i}. {feature}\n"

                ax.text(0.1, 0.9, text_content, transform=ax.transAxes, fontsize=12,
                       verticalalignment='top', fontfamily='monospace',
                       bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.7))
            else:
                ax.text(0.5, 0.5, 'Recommendations not found', ha='center', va='center', transform=ax.transAxes)

        except Exception as e:
            logger.error(f"Error plotting recommendations: {e}")
            ax.text(0.5, 0.5, f'Error: {str(e)}', ha='center', va='center', transform=ax.transAxes)

    def run_comprehensive_visualizations(self):
        """Run all visualization modules with SHAP-compatible data"""
        logger.info("🚀 Starting comprehensive SHAP-based visualizations...")

        # Detect available data
        available_data = self.detect_available_data()

        if not available_data:
            logger.error("❌ No datasets available for visualization")
            return

        # Load SHAP results
        shap_results = self.load_shap_results()

        if shap_results:
            # Create SHAP summary visualization
            self.create_shap_summary_visualization(shap_results)

        # Run individual visualization modules with the best available dataset
        primary_dataset = None
        if 'bias_corrected' in available_data:
            primary_dataset = available_data['bias_corrected']
        elif 'regular_fatigue' in available_data:
            primary_dataset = available_data['regular_fatigue']
        elif 'title_only' in available_data:
            primary_dataset = available_data['title_only']

        if primary_dataset:
            logger.info(f"📊 Using primary dataset: {primary_dataset['path']}")

            try:
                # Run comprehensive feature test
                logger.info("🔍 Running comprehensive feature test...")
                cft.main()
            except Exception as e:
                logger.error(f"❌ Error in comprehensive feature test: {e}")

            try:
                # Run feature importance test
                logger.info("📈 Running feature importance test...")
                ift.main()
            except Exception as e:
                logger.error(f"❌ Error in feature importance test: {e}")

            try:
                # Run all algorithms plot
                logger.info("📊 Running all algorithms comparison...")
                ap.main()
            except Exception as e:
                logger.error(f"❌ Error in all algorithms plot: {e}")

            try:
                # Run k-fold overfitting analysis
                logger.info("🔄 Running k-fold overfitting analysis...")
                kfold.main()
            except Exception as e:
                logger.error(f"❌ Error in k-fold analysis: {e}")

        logger.info("✅ Comprehensive SHAP-based visualizations completed!")
        logger.info(f"📁 Results saved to: {self.output_dir}")

def main():
    """Main function - Updated for SHAP-based analysis"""
    print("🎨 SHAP-Based Visualization Suite")
    print("=" * 50)
    print("Compatible with main5.py SHAP ablation study results")
    print()

    # Initialize visualization suite
    viz_suite = SHAPVisualizationSuite()

    # Run comprehensive visualizations
    viz_suite.run_comprehensive_visualizations()

    print("\n🎉 All visualizations completed!")
    print("📁 Check the following directories for results:")
    print("   • results/shap_visualizations/ (SHAP summary)")
    print("   • results/comprehensive_feature_validation/ (Feature tests)")
    print("   • results/feature_importance_validation/ (Importance tests)")
    print("   • results/all_algorithms_comparison/ (Algorithm comparison)")
    print("   • results/kfold_overfitting_analysis/ (Overfitting analysis)")

if __name__ == "__main__":
    main()
