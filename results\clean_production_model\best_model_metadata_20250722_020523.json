{"algorithm_name": "Random Forest", "accuracy": 0.711864406779661, "f1_score": 0.7218836102529849, "cv_accuracy_mean": 0.676595744680851, "cv_accuracy_std": 0.0372877555128464, "cv_f1_mean": 0.6739146582408697, "cv_f1_std": 0.04149460155950784, "feature_count": 20, "timestamp": "2025-07-22T02:05:23.199208", "model_type": "RandomForestClassifier", "is_best_model": true, "algorithm_key": "random_forest", "random_state": 42, "dataset_path": "dataset/processed/safe_ml_bias_corrected_dataset.csv", "target_column": "corrected_fatigue_risk", "train_samples": 232, "test_samples": 59, "shap_top_features": ["pomokit_unique_words", "total_title_diversity", "avg_time_minutes", "title_balance_ratio", "pomokit_title_count"]}