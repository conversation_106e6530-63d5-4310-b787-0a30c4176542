================================================================================
🔍 SHAP ABLATION STUDY REPORT
================================================================================

📋 DATASET INFORMATION:
   • Dataset Path: dataset/processed/safe_ml_bias_corrected_dataset.csv
   • Target Column: corrected_fatigue_risk
   • Total Features: 20
   • Total Samples: 291
   • Target Distribution: {'medium_risk': 153, 'low_risk': 115, 'high_risk': 23}

🤖 MODEL PERFORMANCE:
   • Logistic Regression:
     - Test Accuracy: 0.6441
     - Test F1-Score: 0.6352
     - CV Accuracy: 0.6204 (±0.0375)
     - CV F1-Score: 0.6169 (±0.0380)
   • Random Forest:
     - Test Accuracy: 0.7119
     - Test F1-Score: 0.7219
     - CV Accuracy: 0.6766 (±0.0373)
     - CV F1-Score: 0.6739 (±0.0415)
   • Gradient Boosting:
     - Test Accuracy: 0.6271
     - Test F1-Score: 0.6257
     - CV Accuracy: 0.6677 (±0.0505)
     - CV F1-Score: 0.6550 (±0.0527)
   • XGBoost:
     - Test Accuracy: 0.7119
     - Test F1-Score: 0.7112
     - CV Accuracy: 0.6549 (±0.0512)
     - CV F1-Score: 0.6444 (±0.0494)

🎯 GLOBAL SHAP FEATURE IMPORTANCE:

   📊 Logistic Regression:
      1. ⭐ pomokit_unique_words: 0.0550
      2. ⭐ total_title_diversity: 0.0542
      3. ⭐ avg_time_minutes: 0.0504
      4. ⭐ title_balance_ratio: 0.0501
      5. 🔸 pomokit_title_count: 0.0485
      6. 🔸 total_time_minutes: 0.0421
      7. 🔸 achievement_rate: 0.0335
      8. 🔸 activity_points: 0.0319
      9. 🔸 consistency_score: 0.0261
     10. 🔸 gamification_balance: 0.0258
     11. 🔸 avg_distance_km: 0.0247
     12. ▫️ total_distance_km: 0.0126
     13. ▫️ productivity_points: 0.0058
     14. ▫️ strava_unique_words: 0.0034
     15. ▫️ activity_days: 0.0017
     16. ▫️ strava_title_count: 0.0015
     17. ▫️ work_days: 0.0009
     18. ▫️ total_cycles: 0.0001
     19. ▫️ weekly_efficiency: 0.0000
     20. ▫️ avg_cycles: 0.0000

   📊 Random Forest:
      1. ⭐ pomokit_unique_words: 0.0534
      2. ⭐ total_title_diversity: 0.0529
      3. ⭐ avg_time_minutes: 0.0502
      4. 🔸 title_balance_ratio: 0.0485
      5. 🔸 pomokit_title_count: 0.0478
      6. 🔸 total_time_minutes: 0.0416
      7. 🔸 achievement_rate: 0.0330
      8. 🔸 activity_points: 0.0294
      9. 🔸 gamification_balance: 0.0264
     10. 🔸 consistency_score: 0.0257
     11. 🔸 avg_distance_km: 0.0253
     12. ▫️ total_distance_km: 0.0116
     13. ▫️ productivity_points: 0.0040
     14. ▫️ strava_unique_words: 0.0019
     15. ▫️ activity_days: 0.0011
     16. ▫️ total_cycles: 0.0001
     17. ▫️ strava_title_count: 0.0000
     18. ▫️ work_days: 0.0000
     19. ▫️ weekly_efficiency: 0.0000
     20. ▫️ avg_cycles: 0.0000

   📊 Gradient Boosting:
      1. ⭐ total_title_diversity: 0.0534
      2. ⭐ pomokit_unique_words: 0.0534
      3. ⭐ avg_time_minutes: 0.0505
      4. 🔸 title_balance_ratio: 0.0491
      5. 🔸 pomokit_title_count: 0.0452
      6. 🔸 total_time_minutes: 0.0425
      7. 🔸 achievement_rate: 0.0338
      8. 🔸 activity_points: 0.0267
      9. 🔸 gamification_balance: 0.0266
     10. 🔸 consistency_score: 0.0251
     11. 🔸 avg_distance_km: 0.0249
     12. ▫️ total_distance_km: 0.0127
     13. ▫️ productivity_points: 0.0037
     14. ▫️ strava_unique_words: 0.0014
     15. ▫️ activity_days: 0.0009
     16. ▫️ work_days: 0.0000
     17. ▫️ weekly_efficiency: 0.0000
     18. ▫️ avg_cycles: 0.0000
     19. ▫️ total_cycles: 0.0000
     20. ▫️ strava_title_count: 0.0000

   📊 XGBoost:
      1. ⭐ pomokit_unique_words: 0.0539
      2. ⭐ total_title_diversity: 0.0524
      3. ⭐ avg_time_minutes: 0.0510
      4. 🔸 title_balance_ratio: 0.0491
      5. 🔸 pomokit_title_count: 0.0454
      6. 🔸 total_time_minutes: 0.0425
      7. 🔸 achievement_rate: 0.0335
      8. 🔸 activity_points: 0.0268
      9. 🔸 gamification_balance: 0.0265
     10. 🔸 consistency_score: 0.0252
     11. 🔸 avg_distance_km: 0.0247
     12. ▫️ total_distance_km: 0.0128
     13. ▫️ productivity_points: 0.0038
     14. ▫️ strava_unique_words: 0.0014
     15. ▫️ activity_days: 0.0008
     16. ▫️ strava_title_count: 0.0000
     17. ▫️ work_days: 0.0000
     18. ▫️ weekly_efficiency: 0.0000
     19. ▫️ avg_cycles: 0.0000
     20. ▫️ total_cycles: 0.0000

🔬 SHAP ANALYSIS ADVANTAGES:
   • Theoretically grounded (Shapley values from game theory)
   • Individual prediction explanations available
   • Captures feature interactions
   • Model-agnostic interpretability
   • Positive/negative contribution analysis

🎖️ FEATURE CONSISTENCY ACROSS ALGORITHMS:
   Most consistent features (appearing in multiple top-5 lists):
     • pomokit_unique_words: 4/4 algorithms (100.0%)
     • total_title_diversity: 4/4 algorithms (100.0%)
     • avg_time_minutes: 4/4 algorithms (100.0%)
     • title_balance_ratio: 4/4 algorithms (100.0%)
     • pomokit_title_count: 4/4 algorithms (100.0%)

✅ SHAP-BASED RECOMMENDATIONS:
   • Best performing algorithm: Random Forest (0.7119 accuracy)
   • Top 5 SHAP features for production:
     1. pomokit_unique_words (SHAP: 0.0534)
     2. total_title_diversity (SHAP: 0.0529)
     3. avg_time_minutes (SHAP: 0.0502)
     4. title_balance_ratio (SHAP: 0.0485)
     5. pomokit_title_count (SHAP: 0.0478)

📊 SHAP ANALYSIS SUMMARY:
   • Total algorithms analyzed: 4
   • Total features analyzed: 20
   • Best accuracy achieved: 0.7119
   • Analysis timestamp: 2025-07-22T02:05:23.009971