================================================================================
K-FOLD CROSS-VALIDATION OVERFITTING ANALYSIS REPORT
================================================================================

📊 DATASET INFORMATION:
   • Total samples: 291
   • Features: 20
   • Target distribution: {'medium_risk': np.int64(153), 'low_risk': np.int64(115), 'high_risk': np.int64(23)}
   • K values tested: 2 to 20

🎯 MODEL PERFORMANCE SUMMARY:
   • Logistic Regression:
     - Best validation: 0.7114 ± 0.0372 (k=4)
     - Training at best k: 0.7285
     - Train-val gap: 0.0171
   • Random Forest:
     - Best validation: 0.7153 ± 0.1048 (k=14)
     - Training at best k: 0.9955
     - Train-val gap: 0.2802
   • Gradient Boosting:
     - Best validation: 0.7257 ± 0.0976 (k=11)
     - Training at best k: 1.0000
     - Train-val gap: 0.2743
   • XGBoost:
     - Best validation: 0.7216 ± 0.1085 (k=14)
     - Training at best k: 1.0000
     - Train-val gap: 0.2784

🔍 OVERFITTING ANALYSIS:
   Models ranked by overfitting risk (low to high):
   1. Logistic Regression - LOW RISK
      • Overfitting score: 9.23
      • Optimal k: 2
      • Max train-val gap: 0.0679
   2. Random Forest - HIGH RISK
      • Overfitting score: 21.10
      • Optimal k: 2
      • Max train-val gap: 0.3214
   3. Gradient Boosting - HIGH RISK
      • Overfitting score: 21.83
      • Optimal k: 18
      • Max train-val gap: 0.3539
   4. XGBoost - HIGH RISK
      • Overfitting score: 21.84
      • Optimal k: 11
      • Max train-val gap: 0.3298

💡 RECOMMENDATIONS:
   • BEST MODEL: Logistic Regression (lowest overfitting risk)
   • RECOMMENDED K: 2
   • HIGH RISK MODELS: Random Forest, Gradient Boosting, XGBoost
   • Consider regularization or feature selection for high-risk models
   • AVERAGE OPTIMAL K: 8.2
================================================================================