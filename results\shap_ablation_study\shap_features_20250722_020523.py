"""
SHAP-Based Optimal Features - Generated 20250722_020523
Best Algorithm: Random Forest (Accuracy: 0.7119)
"""

# All features ranked by SHAP importance
SHAP_RANKED_FEATURES = [
    "pomokit_unique_words",  # SHAP: 0.0534
    "total_title_diversity",  # SHAP: 0.0529
    "avg_time_minutes",  # SHAP: 0.0502
    "title_balance_ratio",  # SHAP: 0.0485
    "pomokit_title_count",  # SHAP: 0.0478
    "total_time_minutes",  # SHAP: 0.0416
    "achievement_rate",  # SHAP: 0.0330
    "activity_points",  # SHAP: 0.0294
    "gamification_balance",  # SHAP: 0.0264
    "consistency_score",  # SHAP: 0.0257
    "avg_distance_km",  # SHAP: 0.0253
    "total_distance_km",  # SHAP: 0.0116
    "productivity_points",  # SHAP: 0.0040
    "strava_unique_words",  # SHAP: 0.0019
    "activity_days",  # SHAP: 0.0011
    "total_cycles",  # SHAP: 0.0001
    "strava_title_count",  # SHAP: 0.0000
    "work_days",  # SHAP: 0.0000
    "weekly_efficiency",  # SHAP: 0.0000
    "avg_cycles",  # SHAP: 0.0000
]

# Top 5 SHAP features
TOP_5_SHAP_FEATURES = [
    "pomokit_unique_words",  # SHAP: 0.0534
    "total_title_diversity",  # SHAP: 0.0529
    "avg_time_minutes",  # SHAP: 0.0502
    "title_balance_ratio",  # SHAP: 0.0485
    "pomokit_title_count",  # SHAP: 0.0478
]

# Top 10 SHAP features
TOP_10_SHAP_FEATURES = [
    "pomokit_unique_words",  # SHAP: 0.0534
    "total_title_diversity",  # SHAP: 0.0529
    "avg_time_minutes",  # SHAP: 0.0502
    "title_balance_ratio",  # SHAP: 0.0485
    "pomokit_title_count",  # SHAP: 0.0478
    "total_time_minutes",  # SHAP: 0.0416
    "achievement_rate",  # SHAP: 0.0330
    "activity_points",  # SHAP: 0.0294
    "gamification_balance",  # SHAP: 0.0264
    "consistency_score",  # SHAP: 0.0257
]

# Top 15 SHAP features
TOP_15_SHAP_FEATURES = [
    "pomokit_unique_words",  # SHAP: 0.0534
    "total_title_diversity",  # SHAP: 0.0529
    "avg_time_minutes",  # SHAP: 0.0502
    "title_balance_ratio",  # SHAP: 0.0485
    "pomokit_title_count",  # SHAP: 0.0478
    "total_time_minutes",  # SHAP: 0.0416
    "achievement_rate",  # SHAP: 0.0330
    "activity_points",  # SHAP: 0.0294
    "gamification_balance",  # SHAP: 0.0264
    "consistency_score",  # SHAP: 0.0257
    "avg_distance_km",  # SHAP: 0.0253
    "total_distance_km",  # SHAP: 0.0116
    "productivity_points",  # SHAP: 0.0040
    "strava_unique_words",  # SHAP: 0.0019
    "activity_days",  # SHAP: 0.0011
]

# SHAP importance scores
SHAP_IMPORTANCE_SCORES = {
    "pomokit_unique_words": 0.053423,
    "total_title_diversity": 0.052911,
    "avg_time_minutes": 0.050163,
    "title_balance_ratio": 0.048456,
    "pomokit_title_count": 0.047803,
    "total_time_minutes": 0.041563,
    "achievement_rate": 0.033034,
    "activity_points": 0.029375,
    "gamification_balance": 0.026381,
    "consistency_score": 0.025686,
    "avg_distance_km": 0.025327,
    "total_distance_km": 0.011628,
    "productivity_points": 0.003958,
    "strava_unique_words": 0.001892,
    "activity_days": 0.001067,
    "total_cycles": 0.000137,
    "strava_title_count": 0.000019,
    "work_days": 0.000000,
    "weekly_efficiency": 0.000000,
    "avg_cycles": 0.000000,
}

# Usage example:
# X_shap = X[TOP_5_SHAP_FEATURES]
# model.fit(X_shap, y)