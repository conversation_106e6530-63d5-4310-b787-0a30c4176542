#!/usr/bin/env python3
"""
Test script to verify that the chart fix is working correctly
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.model_selection import cross_val_score, StratifiedKFold
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.pipeline import Pipeline
from pathlib import Path

# Try to import XGBoost
try:
    from xgboost import XGBClassifier
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False

def test_chart_fix():
    """Test that all algorithms are displayed correctly in the chart"""
    
    print("🧪 Testing chart fix for all algorithms...")
    
    # Load data
    data_path = "dataset/processed/safe_ml_bias_corrected_dataset.csv"
    if not Path(data_path).exists():
        print(f"❌ Data file not found: {data_path}")
        return False
    
    data = pd.read_csv(data_path)
    X = data.drop(columns=['corrected_fatigue_risk'])
    y = data['corrected_fatigue_risk']
    
    # Encode target for XGBoost
    label_encoder = LabelEncoder()
    y_encoded = label_encoder.fit_transform(y)
    
    # Test features (use first 10 features)
    test_features = list(X.columns[:10])
    X_test = X[test_features]
    
    # Setup CV
    cv = StratifiedKFold(n_splits=3, shuffle=True, random_state=42)
    
    # Test algorithms
    algorithms = {
        'Logistic Regression': Pipeline([
            ('scaler', StandardScaler()),
            ('lr', LogisticRegression(random_state=42, max_iter=1000))
        ]),
        'Random Forest': RandomForestClassifier(n_estimators=50, random_state=42),
        'Gradient Boosting': GradientBoostingClassifier(n_estimators=50, random_state=42),
    }
    
    if XGBOOST_AVAILABLE:
        algorithms['XGBoost'] = XGBClassifier(n_estimators=50, random_state=42, eval_metric='mlogloss')
    
    results = {}
    
    for algo_name, model in algorithms.items():
        try:
            # Use appropriate target
            y_to_use = y_encoded if algo_name == 'XGBoost' else y
            
            # Get scores
            scores = cross_val_score(model, X_test, y_to_use, cv=cv, scoring='accuracy')
            mean_score = scores.mean()
            std_score = scores.std()
            
            results[algo_name] = (mean_score, std_score)
            print(f"✅ {algo_name}: {mean_score:.3f} ± {std_score:.3f}")
            
        except Exception as e:
            print(f"❌ Error with {algo_name}: {e}")
            return False
    
    # Create test chart
    fig, axes = plt.subplots(2, 2, figsize=(12, 8))
    fig.suptitle('Chart Fix Test - All Algorithms', fontsize=14, fontweight='bold')
    
    axes_flat = axes.flatten()
    colors = ['orange', 'skyblue', 'lightcoral', 'lightgreen']
    
    for i, (algo_name, (mean_score, std_score)) in enumerate(results.items()):
        if i >= len(axes_flat):
            break
            
        ax = axes_flat[i]
        
        # Create simple bar
        bars = ax.bar([0], [mean_score], yerr=[std_score], capsize=5, 
                     color=colors[i], alpha=0.8, edgecolor='black')
        
        # Set dynamic y-axis range
        min_val = max(0.0, mean_score - std_score - 0.05)
        max_val = min(1.0, mean_score + std_score + 0.05)
        
        # Ensure minimum range
        if max_val - min_val < 0.2:
            center = (max_val + min_val) / 2
            min_val = max(0.0, center - 0.1)
            max_val = min(1.0, center + 0.1)
        
        ax.set_ylim(min_val, max_val)
        ax.set_title(f'{algo_name}', fontweight='bold')
        ax.set_ylabel('Accuracy')
        ax.set_xticks([0])
        ax.set_xticklabels(['Test'])
        ax.grid(True, alpha=0.3)
        
        # Add value label
        y_range = max_val - min_val
        label_offset = y_range * 0.02
        ax.text(0, mean_score + std_score + label_offset, f'{mean_score:.3f}', 
               ha='center', va='bottom', fontweight='bold')
    
    # Hide unused subplot if needed
    if len(results) < 4:
        axes_flat[3].set_visible(False)
    
    plt.tight_layout()
    
    # Save test chart
    output_dir = Path("results/test_charts")
    output_dir.mkdir(parents=True, exist_ok=True)
    plt.savefig(output_dir / 'chart_fix_test.png', dpi=300, bbox_inches='tight')
    
    print(f"✅ Test chart saved to: {output_dir / 'chart_fix_test.png'}")
    plt.show()
    
    # Verify all algorithms have reasonable ranges
    all_good = True
    for algo_name, (mean_score, std_score) in results.items():
        if mean_score < 0.5 or mean_score > 1.0:
            print(f"❌ {algo_name} has unreasonable score: {mean_score}")
            all_good = False
        else:
            print(f"✅ {algo_name} score is reasonable: {mean_score:.3f}")
    
    return all_good

if __name__ == "__main__":
    success = test_chart_fix()
    
    if success:
        print("\n🎉 Chart fix test PASSED!")
        print("✅ All algorithms should now be visible in charts")
        print("✅ Y-axis ranges are dynamically adjusted")
        print("✅ Value labels are properly positioned")
    else:
        print("\n💥 Chart fix test FAILED!")
        print("❌ There may still be issues with the chart display")
