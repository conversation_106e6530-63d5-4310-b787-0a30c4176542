"""
Create Feature Set Comparison Plots for All 4 Algorithms
- Logistic Regression
- Random Forest  
- Gradient Boosting
- XGBoost

All in one comprehensive visualization (2x2 grid)
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.model_selection import cross_val_score, StratifiedKFold
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.pipeline import Pipeline
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Try to import XGBoost
try:
    from xgboost import XGBClassifier
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False
    logger.warning("XGBoost not available")

class AllAlgorithmsComparison:
    """Comprehensive comparison of all 4 algorithms"""
    
    def __init__(self):
        self.X = None
        self.y = None
        self.y_encoded = None
        self.consensus_features = []
        self.scenarios = {}
        self.results = {}
        
    def load_data_and_features(self):
        """Load data and feature sets"""
        # Load data
        data_path = "dataset/processed/safe_ml_bias_corrected_dataset.csv"
        data = pd.read_csv(data_path)
        
        self.X = data.drop(columns=['corrected_fatigue_risk'])
        self.y = data['corrected_fatigue_risk']
        
        # Encode target for XGBoost
        label_encoder = LabelEncoder()
        self.y_encoded = label_encoder.fit_transform(self.y)
        
        # Load consensus features
        consensus_files = list(Path("results/feature_selection/selected_features/").glob("consensus_features_*.txt"))
        
        if consensus_files:
            with open(consensus_files[0], 'r') as f:
                for line in f:
                    if line.strip() and not line.startswith('#'):
                        feature_name = line.split()[0].strip()
                        if feature_name in self.X.columns:
                            self.consensus_features.append(feature_name)
        
        logger.info(f"Loaded {len(self.consensus_features)} consensus features")
        
    def define_scenarios(self):
        """Define feature scenarios"""
        # Set random seed for reproducible random features
        np.random.seed(42)
        
        self.scenarios = {
            'All Features\n(18 features)': list(self.X.columns),
            'Consensus Features\n(10 features)': self.consensus_features,
            'Top 10 RF Features\n(10 features)': self.consensus_features,  # Same as consensus
            'Top 5 Consensus\n(5 features)': self.consensus_features[:5],
            'Top 3 Consensus\n(3 features)': self.consensus_features[:3],
            'Random 10 Features\n(10 features)': list(np.random.choice(self.X.columns, 10, replace=False)),
            'Random 5 Features\n(5 features)': list(np.random.choice(self.X.columns, 5, replace=False))
        }
        
    def test_algorithm(self, algorithm_name):
        """Test one algorithm across all scenarios"""
        
        # Define algorithm
        if algorithm_name == 'Logistic Regression':
            model = Pipeline([
                ('scaler', StandardScaler()),
                ('lr', LogisticRegression(random_state=42, max_iter=1000))
            ])
            y_to_use = self.y
        elif algorithm_name == 'Random Forest':
            model = RandomForestClassifier(n_estimators=100, random_state=42)
            y_to_use = self.y
        elif algorithm_name == 'Gradient Boosting':
            model = GradientBoostingClassifier(n_estimators=100, random_state=42)
            y_to_use = self.y
        elif algorithm_name == 'XGBoost' and XGBOOST_AVAILABLE:
            model = XGBClassifier(n_estimators=100, random_state=42, eval_metric='mlogloss')
            y_to_use = self.y_encoded
        else:
            return None
        
        # Setup CV
        cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
        
        # Test each scenario
        scenario_names = []
        means = []
        stds = []
        
        for scenario_name, features in self.scenarios.items():
            if not features:
                continue
                
            X_subset = self.X[features]
            
            try:
                scores = cross_val_score(model, X_subset, y_to_use, cv=cv, scoring='accuracy')
                scenario_names.append(scenario_name)
                means.append(scores.mean())
                stds.append(scores.std())
                
                logger.info(f"{algorithm_name} - {scenario_name.replace(chr(10), ' ')}: {scores.mean():.3f} ± {scores.std():.3f}")
                
            except Exception as e:
                logger.error(f"Error with {scenario_name}: {str(e)}")
                continue
        
        return scenario_names, means, stds
    
    def test_all_algorithms(self):
        """Test all algorithms"""
        algorithms = ['Logistic Regression', 'Random Forest', 'Gradient Boosting']
        
        if XGBOOST_AVAILABLE:
            algorithms.append('XGBoost')
        
        for algo in algorithms:
            logger.info(f"\nTesting {algo}...")
            result = self.test_algorithm(algo)
            if result:
                self.results[algo] = result
        
        return self.results
    
    def create_comprehensive_plot(self):
        """Create comprehensive 2x2 plot for all algorithms"""
        
        # Define colors for each algorithm
        colors = {
            'Logistic Regression': '#FFA500',  # Orange
            'Random Forest': '#87CEEB',        # Sky Blue  
            'Gradient Boosting': '#F08080',    # Light Coral
            'XGBoost': '#90EE90'               # Light Green
        }
        
        # Create 2x2 subplot
        fig, axes = plt.subplots(2, 2, figsize=(20, 12))
        fig.suptitle('Feature Set Comparison: All 4 Algorithms', fontsize=18, fontweight='bold', y=0.98)
        
        # Flatten axes for easier iteration
        axes_flat = axes.flatten()
        
        # Plot each algorithm
        for i, (algo_name, (scenario_names, means, stds)) in enumerate(self.results.items()):
            if i >= len(axes_flat):
                break
                
            ax = axes_flat[i]
            color = colors.get(algo_name, 'gray')
            
            # Create bars
            x_pos = range(len(scenario_names))
            bars = ax.bar(x_pos, means, yerr=stds, capsize=5, alpha=0.8, 
                         color=color, edgecolor='black', linewidth=0.5)
            
            # Customize subplot
            ax.set_xlabel('Feature Scenarios', fontsize=12, fontweight='bold')
            ax.set_ylabel('Accuracy', fontsize=12, fontweight='bold')
            ax.set_title(f'{algo_name}: Feature Set Comparison', fontsize=14, fontweight='bold')
            ax.set_xticks(x_pos)
            ax.set_xticklabels(scenario_names, rotation=45, ha='right', fontsize=9)
            ax.grid(True, alpha=0.3, axis='y')
            
            # Set appropriate y-axis limits
            if algo_name == 'Logistic Regression':
                ax.set_ylim(0.0, 0.7)  # Lower range for LR
            else:
                ax.set_ylim(0.85, 1.0)  # High performance range for tree models
            
            # Add value labels on bars
            for bar, mean, std in zip(bars, means, stds):
                height = bar.get_height()
                # Adjust label position based on algorithm
                if algo_name == 'Logistic Regression':
                    label_y = height + std + 0.01
                else:
                    label_y = height + std + 0.002
                    
                ax.text(bar.get_x() + bar.get_width()/2, label_y,
                       f'{mean:.3f}', ha='center', va='bottom', 
                       fontweight='bold', fontsize=9)
        
        # Hide unused subplot if XGBoost not available
        if len(self.results) < 4:
            axes_flat[3].set_visible(False)
        
        plt.tight_layout()
        
        # Save plot
        output_dir = Path("results/all_algorithms_comparison")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        plt.savefig(output_dir / 'all_four_algorithms_comparison.png', 
                   dpi=300, bbox_inches='tight')
        logger.info("✅ Saved comprehensive all algorithms comparison plot")
        
        plt.show()
        
        return fig
    
    def create_summary_table(self):
        """Create comprehensive summary table"""
        
        print("\n" + "="*100)
        print("COMPREHENSIVE FEATURE COMPARISON SUMMARY - ALL 4 ALGORITHMS")
        print("="*100)
        
        # Find best performance for each algorithm
        best_performances = {}
        
        for algo_name, (scenario_names, means, stds) in self.results.items():
            # Sort by performance
            sorted_results = sorted(zip(scenario_names, means, stds), key=lambda x: x[1], reverse=True)
            best_name, best_mean, best_std = sorted_results[0]
            best_performances[algo_name] = (best_name, best_mean, best_std)
            
            print(f"\n🎯 {algo_name.upper()} RESULTS:")
            print("-" * 60)
            
            for i, (name, mean, std) in enumerate(sorted_results):
                clean_name = name.replace('\n', ' ')
                print(f"{i+1:2d}. {clean_name:<30} {mean:.3f} ± {std:.3f}")
            
            print(f"\n🏆 BEST: {best_name.replace(chr(10), ' ')} - {best_mean:.3f} ± {best_std:.3f}")
        
        # Overall ranking by best performance
        print(f"\n🏆 OVERALL ALGORITHM RANKING (by best performance):")
        print("-" * 60)
        
        overall_ranking = sorted(best_performances.items(), key=lambda x: x[1][1], reverse=True)
        
        for i, (algo, (scenario, mean, std)) in enumerate(overall_ranking):
            print(f"{i+1}. {algo:<20} {mean:.3f} ± {std:.3f} ({scenario.replace(chr(10), ' ')})")
        
        # Top 3 Consensus comparison
        print(f"\n🎯 TOP 3 CONSENSUS FEATURES COMPARISON:")
        print("-" * 60)
        
        top3_results = []
        for algo_name, (scenario_names, means, stds) in self.results.items():
            for scenario, mean, std in zip(scenario_names, means, stds):
                if 'Top 3 Consensus' in scenario:
                    top3_results.append((algo_name, mean, std))
                    break
        
        # Sort Top 3 results
        top3_results.sort(key=lambda x: x[1], reverse=True)
        
        for i, (algo, mean, std) in enumerate(top3_results):
            print(f"{i+1}. {algo:<20} {mean:.3f} ± {std:.3f}")
        
        # Feature efficiency analysis
        print(f"\n⚡ FEATURE EFFICIENCY ANALYSIS (Top 3 vs All Features):")
        print("-" * 60)
        
        for algo_name, (scenario_names, means, stds) in self.results.items():
            top3_perf = None
            all_perf = None
            
            for scenario, mean, std in zip(scenario_names, means, stds):
                if 'Top 3 Consensus' in scenario:
                    top3_perf = mean
                elif 'All Features' in scenario:
                    all_perf = mean
            
            if top3_perf and all_perf:
                improvement = top3_perf - all_perf
                efficiency = top3_perf / (3/20)  # Performance per feature ratio
                
                print(f"{algo_name:<20} Top3: {top3_perf:.3f} | All: {all_perf:.3f} | "
                      f"Improvement: {improvement:+.3f} | Efficiency: {efficiency:.2f}")
        
        print("\n" + "="*100)
    
    def run_comprehensive_analysis(self):
        """Run complete analysis"""
        
        # Load data
        self.load_data_and_features()
        
        # Define scenarios
        self.define_scenarios()
        
        # Test all algorithms
        self.test_all_algorithms()
        
        # Create comprehensive plot
        self.create_comprehensive_plot()
        
        # Create summary table
        self.create_summary_table()
        
        return self.results


def main():
    """Main function"""
    
    # Check for data file
    data_path = "dataset/processed/safe_ml_bias_corrected_dataset.csv"
    if not Path(data_path).exists():
        print(f"❌ Data file not found: {data_path}")
        return
    
    # Check for SHAP results
    shap_dir = Path("results/feature_selection")
    if not shap_dir.exists():
        print("❌ SHAP results not found. Please run main1.py first.")
        return
    
    print("🚀 Creating comprehensive feature comparison for all 4 algorithms...")
    
    # Run comprehensive analysis
    analyzer = AllAlgorithmsComparison()
    results = analyzer.run_comprehensive_analysis()
    
    if results:
        print("\n🎉 Comprehensive algorithm comparison completed successfully!")
        print("📁 Results saved to: results/all_algorithms_comparison/")
        print("📊 Plot: all_four_algorithms_comparison.png")
    else:
        print("❌ No results generated. Check your setup.")


if __name__ == "__main__":
    main()
